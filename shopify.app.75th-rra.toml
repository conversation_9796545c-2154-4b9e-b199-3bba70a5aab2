# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "566e2a972b89ccf6da72959fcdd53084"
application_url = "https://satyr-up-vulture.ngrok-free.app"
embedded = true
name = "AUI 75th RRA"
handle = "aui-75th-rra"

[webhooks]
api_version = "2025-04"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled", "orders/create", "products/update" ]
  uri = "/webhooks/app"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_products, read_orders, write_orders"

[auth]
redirect_urls = [
  "https://satyr-up-vulture.ngrok-free.app/auth/callback",
  "https://satyr-up-vulture.ngrok-free.app/auth/shopify/callback",
  "https://satyr-up-vulture.ngrok-free.app/api/auth/callback"
]

[pos]
embedded = false

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true
