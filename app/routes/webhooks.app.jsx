import { authenticate } from "../shopify.server";
import shopify from "../shopify.server";
import db from "../db.server"
import { container } from "../lib/container/ServiceContainer.server.js";
import { hasProcessedWebhook, markWebhookProcessed, hasProcessedOrder, checkAndMarkEventProcessed } from "../models/ProcessedWebhook.server";

//! TODO - Set up webhooks to process updated, cancelled, and fulfilled orders
// fulfilled orders need to have the calculated shipping costs added for the day they were fulfilled

export const action = async({ request }) => {
    const { shop, topic, payload, session } = await authenticate.webhook(request);

    // Create a new admin client
    const adminSession = await shopify.sessionStorage.loadSession(shop);
    const admin = new shopify.api.clients.Graphql({ session: adminSession });

    // Extract the event ID and subscription ID from the headers
    const eventId = request.headers.get('X-Shopify-Event-Id') || null;
    const subscriptionId = request.headers.get('X-Shopify-Webhook-Id') || 'unknown';

    // Log the webhook details including event ID and subscription ID
    console.log(`Received ${topic} webhook for ${shop} with event ID: ${eventId}, subscription ID: ${subscriptionId}`);

    if (!eventId) {
        console.error('No X-Shopify-Event-Id header found in webhook request');
    }

    // Generate a unique ID for this webhook that includes the subscription ID
    // This ensures we don't process the same order twice from different subscriptions
    let webhookId;

    if (topic.startsWith('ORDERS_') && payload.id) {
        // For orders, include both order ID and subscription ID to ensure uniqueness
        webhookId = `order-${String(payload.id)}-${subscriptionId}`;
    } else if (topic.startsWith('PRODUCTS_') && payload.id) {
        // For products, include both product ID and subscription ID
        webhookId = `product-${String(payload.id)}-${subscriptionId}`;
    } else {
        // For other webhooks, generate a random ID that includes the subscription ID
        webhookId = `${topic}-${subscriptionId}-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
    }

    // Log the webhook ID for debugging
    console.log(`Generated webhookId: ${webhookId} (type: ${typeof webhookId})`);
    console.log(`Original payload.id: ${payload.id} (type: ${typeof payload.id})`);

    // Ensure webhookId is always a string
    webhookId = String(webhookId);

    // Extract the raw order ID if this is an order webhook
    let orderId = null;
    if (topic.startsWith('ORDERS_') && payload.id) {
        orderId = String(payload.id);
    }

    // Use atomic check-and-mark operation for event ID to prevent race conditions
    if (eventId) {
        try {
            // This will both check if the event has been processed AND mark it as processed in one operation
            // This prevents race conditions where two webhooks with the same event ID arrive close together
            const eventAlreadyProcessed = await checkAndMarkEventProcessed(shop, eventId, topic, orderId);
            if (eventAlreadyProcessed) {
                console.log(`Event ${eventId} has already been processed. Skipping.`);
                return new Response(null, { status: 200 });
            }
            // If we get here, the event was not previously processed but has now been marked as processed
            console.log(`Event ${eventId} is now marked as processed. Continuing with processing.`);
        } catch (error) {
            // If there's an error with the atomic operation, log it but continue with traditional checks
            console.error(`Error with atomic check-and-mark: ${error.message}`);
            // Fall through to traditional checks
        }
    } else {
        // No event ID available, use traditional checks
        console.warn('No event ID available in webhook. Using fallback deduplication methods.');

        // Fallback to checking the webhook ID if no event ID is available
        try {
            const alreadyProcessed = await hasProcessedWebhook(shop, webhookId);
            if (alreadyProcessed) {
                console.log(`Webhook ${webhookId} for ${topic} has already been processed. Skipping.`);
                return new Response(null, { status: 200 });
            }
        } catch (error) {
            // If there's an error checking the webhook status, log it but continue processing
            console.error(`Error checking webhook status: ${error.message}`);
            // Don't return here, continue processing the webhook
        }

        // For order webhooks, also check if we've already processed this order from a different subscription
        if (orderId) {
            try {
                const orderAlreadyProcessed = await hasProcessedOrder(shop, orderId);
                if (orderAlreadyProcessed) {
                    console.log(`Order ${orderId} has already been processed from a different webhook. Skipping.`);

                    // Still mark this webhook as processed to avoid future processing
                    await markWebhookProcessed(shop, topic, webhookId, orderId, subscriptionId, eventId);

                    return new Response(null, { status: 200 });
                }
            } catch (error) {
                // If there's an error checking the order status, log it but continue processing
                console.error(`Error checking order status: ${error.message}`);
                // Don't return here, continue processing the webhook
            }
        }

        // If we get here with no event ID, we need to mark this webhook as processed now
        // to prevent race conditions with other webhooks
        try {
            await markWebhookProcessed(shop, topic, webhookId, orderId, subscriptionId, eventId);
        } catch (error) {
            // If there's an error marking the webhook as processed, log it but continue processing
            console.error(`Error marking webhook as processed: ${error.message}`);
        }
    }

    switch (topic) {
        case "APP_UNINSTALLED":
            if (session) {
                await db.session.deleteMany({ where: { shop }})
            }
            break;
        case "ORDERS_CREATE":
            /* //! PAYLOAD - Same for Created, Updated, Fulfilled, and Cancelled
                {
                    id: string
                    admin_graphql_api_id: string (ID) - the full GID version of the order ID (gid://shopify/Order/<id>)
                    cancel_reason: string
                    fulfillment_status: string
                    line_items: [
                        {
                            id: string
                            admin_graphql_api_id: string (ID)
                            fulfillment_status: string
                            title: string
                            variant_id: string
                            variant_title: string
                            sku: string
                            quantity: int
                        }
                    ]
                }
            */


            // todo - offload async operations to external functionality - return quickly to avoid timeouts
            async function getOrderWithFulfillmentInfo(orderId) {
                return await admin.graphql(
                    `
                    query GetOrderWithFulfillmentInfo($orderId: ID!) {
                        order(id: $orderId) {
                            id
                            fulfillmentOrders(first: 10) {
                                edges {
                                    node {
                                        id
                                        assignedLocation {
                                            location {
                                                id
                                                name
                                                fulfillmentService {
                                                    id
                                                    serviceName
                                                    type
                                                }
                                            }
                                        }
                                        lineItems(first: 50) {
                                            edges {
                                                node {
                                                    id
                                                    lineItem {
                                                        variant {
                                                            id
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    `, {
                        "variables": {
                            orderId: orderId,
                        }
                    }
                )
            }

            async function getProductType(id) {
                return await admin.graphql(
                    `
                    query GetProduct($id: ID!) {
                        product(id: $id) {
                            productType
                            variants(first: 10) {
                                edges {
                                    node {
                                        id
                                    }
                                }
                            }
                        }
                    }
                    `, {
                        "variables": {
                            id: id,
                        }
                    }
                )
            }
            // First, get the order with fulfillment information
            const orderGid = `gid://shopify/Order/${payload.id}`;
            console.log(`Fetching order information for ${orderGid}`);

            let orderResponse;
            try {
                orderResponse = await getOrderWithFulfillmentInfo(orderGid);
            } catch (error) {
                console.error(`Error fetching order information: ${error.message}`);
                // Continue processing even if we can't get fulfillment info
            }

            // Extract fulfillment information if available
            let fulfillmentInfo = {};
            if (orderResponse) {
                const orderData = await orderResponse.json();
                console.log('DEBUG - Full order response:', JSON.stringify(orderData, null, 2));

                if (orderData.data && orderData.data.order && orderData.data.order.fulfillmentOrders) {
                    // Create a map of variant IDs to their fulfillment service information
                    const fulfillmentOrders = orderData.data.order.fulfillmentOrders.edges.map(edge => edge.node);
                    console.log(`DEBUG - Found ${fulfillmentOrders.length} fulfillment orders`);

                    for (const fulfillmentOrder of fulfillmentOrders) {
                        console.log(`DEBUG - Processing fulfillment order: ${fulfillmentOrder.id}`);

                        const location = fulfillmentOrder.assignedLocation.location;
                        console.log(`DEBUG - Location: ${location.name}`);

                        const fulfillmentService = location.fulfillmentService;
                        console.log(`DEBUG - Fulfillment service: ${fulfillmentService ? JSON.stringify(fulfillmentService) : 'null'}`);

                        // Get all variant IDs in this fulfillment order
                        const lineItems = fulfillmentOrder.lineItems.edges.map(edge => edge.node);
                        console.log(`DEBUG - Found ${lineItems.length} line items in this fulfillment order`);

                        for (const lineItem of lineItems) {
                            if (lineItem.lineItem && lineItem.lineItem.variant) {
                                const variantId = lineItem.lineItem.variant.id;
                                console.log(`DEBUG - Found variant ID in fulfillment order: ${variantId}`);

                                // Store fulfillment service info for this variant
                                fulfillmentInfo[variantId] = {
                                    locationName: location.name,
                                    fulfillmentService: fulfillmentService ? {
                                        serviceName: fulfillmentService.serviceName,
                                        type: fulfillmentService.type
                                    } : null
                                };

                                console.log(`DEBUG - Stored fulfillment info for variant ${variantId}:`,
                                    JSON.stringify(fulfillmentInfo[variantId], null, 2));
                            } else {
                                console.log(`DEBUG - Line item missing variant information:`, JSON.stringify(lineItem, null, 2));
                            }
                        }
                    }

                    console.log(`Found fulfillment information for ${Object.keys(fulfillmentInfo).length} variants`);
                } else {
                    console.log('DEBUG - Missing order or fulfillment orders in response');
                    if (orderData.errors) {
                        console.error('GraphQL errors:', JSON.stringify(orderData.errors, null, 2));
                    }
                }
            } else {
                console.log('DEBUG - No order response available');
            }

            // Use the OrderProcessingService from container to process the entire order
            try {
                const orderProcessingService = await container.resolve('orderProcessingService');

                console.log(`Processing order ${payload.id} for shop ${shop} using OrderProcessingService`);

                const result = await orderProcessingService.processOrder(
                    payload.id,
                    shop,
                    {
                        skipValidation: false, // Validate the order
                        dryRun: false, // Actually process and save
                        forceReprocess: false // Don't reprocess if already done
                    }
                );

                if (result.success) {
                    console.log(`Successfully processed order ${payload.id}:`, result);
                } else {
                    console.error(`Failed to process order ${payload.id}:`, result);
                }

            } catch (error) {
                console.error(`Error processing order ${payload.id} with OrderProcessingService:`, error);

                // Log the error for debugging but don't throw to avoid webhook failures
                // The OrderProcessingService already handles logging internally
            }


            break;
        case "PRODUCTS_UPDATE":
            try {
                // Get services from container
                const transactionProcessingService = await container.resolve('transactionProcessingService');

                // Get unprocessables for this shop
                const unprocessableStats = await transactionProcessingService.getUnprocessableStatistics(shop);
                if (!unprocessableStats || unprocessableStats.total === 0) {
                    break;
                }

                let productType = payload.product_type;
                let productId = payload.id;

                // Process each variant
                for (const variant of payload.variants) {
                    const variantId = variant.id;

                    // Try to reprocess any matching unprocessable items
                    const result = await transactionProcessingService.retrySpecificUnprocessable(
                        shop,
                        variantId,
                        productId,
                        productType
                    );

                    if (result) {
                        console.log(`Successfully reprocessed unprocessable item for variant ${variantId} of product ${productId}`);
                    }
                }
            } catch (error) {
                console.error(`Error in PRODUCTS_UPDATE webhook: ${error.message}`);
            }
            break
        case "ORDERS_DELETE":
            // occurs when an order is deleted
            //! payload: { id: string }
        case "ORDERS_UPDATED":
            //! orders/create, orders/delete, orders/fulfilled and orders/updated return the same payload
        case "ORDERS_EDITED":
            // occurs when an order is edited
            // payload:
            /*
                {
                    "order_edit": {
                        id: string
                        created_at: datetime
                        committed_at: datetime
                        order_id: string
                        user_id: null
                        line_items: {
                            additions: [
                                {
                                    id: string
                                    delta: int
                                }
                            ],
                            removals: [
                                {
                                    id: string
                                    delta: int
                                }
                            ]
                        }
                    }
                }
            */
        case "ORDERS_FULFILLED":
            //! orders/create, orders/delete, orders/fulfilled and orders/updated return the same payload
        case "ORDERS_CANCELLED":
            //! orders/create, orders/delete, orders/fulfilled and orders/updated return the same payload

        case "CUSTOMERS_DATA_REQUEST":
        case "CUSTOMERS_REDACT":
        case "SHOP_REDACT":
        default:
            throw new Response("Unhandled webhook topic", { status: 404 })
    }

    // Only mark the webhook as processed if we didn't already do it with the event ID
    // (If we used checkAndMarkEventProcessed successfully, we don't need to do this again)
    if (!eventId) {
        // We already marked it as processed earlier in the code for non-event ID webhooks
        console.log('Webhook was already marked as processed earlier.');
    } else {
        // For event ID webhooks, we don't need to mark it again since checkAndMarkEventProcessed did it
        console.log(`Event ${eventId} was already marked as processed by checkAndMarkEventProcessed.`);
    }

    return new Response(null, { status: 200 });
}


// products/create - can pull id, title, variants titles and skus, etc.
// products/delete
// products/update
