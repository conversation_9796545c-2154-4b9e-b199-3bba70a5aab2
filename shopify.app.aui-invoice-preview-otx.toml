# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "dd3a1de4cfaa6f4d52b723c7f7a19d71"
application_url = "https://otx-invoice-preview.fly.dev/"
embedded = true
name = "aui-invoice-preview-otx"
handle = "aui-invoice-preview-otx"

[webhooks]
api_version = "2025-01"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled", "orders/create" ]
  uri = "/webhooks/app"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_orders,read_products"

[auth]
redirect_urls = [
  "https://otx-invoice-preview.fly.dev/auth/callback",
  "https://otx-invoice-preview.fly.dev/auth/shopify/callback",
  "https://otx-invoice-preview.fly.dev/api/auth/callback"
]

[pos]
embedded = false
